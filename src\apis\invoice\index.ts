/*
 * @description:
 * @Author: lexy
 * @Date: 2023-08-16 15:30:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-18 15:59:53
 */
import { get, post, put, del, patch } from '../http'

/**
 * @description 获取发票设置
 * @param
 * @returns
 */
export const doGetinvoiceSettings = (params: { invoiceSettingsClientType: 'SHOP' | 'SUPPLIER'; shopId: string }) => {
    return get({ url: `addon-invoice/invoice/invoiceSettings/`, params })
}
/**
 * @description 编辑发票设置
 * @param
 * @returns
 */
export const doPostinvoiceSettings = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceSettings/`, data })
}
/**
 * @description 分页查询开票申请
 * @param
 * @returns
 */
export const doGetinvoiceRequestList = (params: any) => {
    return get({ url: `addon-invoice/invoice/invoiceRequest/`, params })
}
/**
 * @description 查询发票详情
 * @param
 * @returns
 */
export const doGetinvoiceDetail = (id: string) => {
    return get({ url: `addon-invoice/invoice/invoiceRequest/${id}` })
}
/**
 * @description 拒绝开票申请
 * @param
 * @returns
 */
export const doPostrefuseInvoiceRequest = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceRequest/refuseInvoiceRequest`, data })
}
/**
 * @description 上传发票附件/重新上传发票附件
 * @param
 * @returns
 */
export const doPostinvoiceAttachment = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceAttachment/upload `, data })
}

/**
 * @description 发票申请预检查
 * @param orderNos 订单号
 * @param invoiceOwnerType 发票所属方类型，默认为 SUPPLIER（供应商端）
 * @returns
 */
export const doGetInvoicePreRequest = (orderNos: string, invoiceOwnerType: string = 'SUPPLIER') => {
    return get({
        url: `addon-invoice/invoice/invoiceRequest/pre-request`,
        params: { orderNos, invoiceOwnerType },
    })
}

/**
 * @description 创建发票申请
 * @param data 发票申请数据
 * @returns
 */
export const doPostInvoiceRequest = (data: {
    invoiceOwnerType: string // 发票所属类型（必填）
    orderNos: string[] // 订单号集合（必填）
    invoiceType: string // 发票类型（必填）
    billingRemarks: string // 开票备注（可选）
    invoiceHeaderId: number // 发票抬头ID（必填）
}) => {
    return post({ url: `addon-invoice/invoice/invoiceRequest`, data })
}

/**
 * @description 查询默认发票抬头
 * @returns
 */
export const doGetDefaultInvoiceHeader = () => {
    return get({ url: `addon-invoice/invoice/invoice-headers/getDefaultInvoiceHeader` })
}

/**
 * @description 查询发票抬头信息
 * @param id 抬头ID
 * @returns
 */
export const doGetInvoiceHeaders = (id?: string) => {
    const url = id ? `addon-invoice/invoice/invoice-headers/${id}` : `addon-invoice/invoice/invoice-headers`
    return get({ url })
}

/**
 * @description 分页查询发票抬头列表
 * @param params 查询参数
 * @returns
 */
export const doGetInvoiceHeadersList = (params: { ownerType: string; current?: number; size?: number }) => {
    return get({ url: `addon-invoice/invoice/invoice-headers/pageInvoiceHeader`, params })
}

/**
 * @description 创建发票抬头
 * @param data 抬头数据
 * @returns
 */
export const doPostInvoiceHeader = (data: {
    invoiceHeaderType: 'PERSONAL' | 'ENTERPRISE'
    header: string
    taxIdentNo?: string
    openingBank?: string
    bankAccountNo?: string
    enterpriseAddress?: string
    enterprisePhone?: string
    email?: string
    isDefault?: boolean
    // 保持向后兼容
    headerType?: 'PERSONAL' | 'COMPANY'
    personalName?: string
    companyName?: string
    taxNo?: string
    bankName?: string
    bankAccount?: string
    companyAddress?: string
    companyPhone?: string
}) => {
    return post({ url: `addon-invoice/invoice/invoice-headers`, data })
}

/**
 * @description 下载发票附件 - 使用URL参数方式
 * @param orderNo 订单号
 * @returns
 */
export const doDownloadInvoiceAttachment = (orderNo: string) => {
    return post({
        url: `addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachmentByShop`,
        params: { orderNo },
        responseType: 'blob'
    })
}

/**
 * @description 下载发票附件 - 使用表单数据方式
 * @param orderNo 订单号
 * @returns
 */
export const doDownloadInvoiceAttachmentByForm = (orderNo: string) => {
    return post({
        url: `addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachmentByShop`,
        data: { orderNo },
        responseType: 'blob'
    })
}

/**
 * @description 撤销发票申请
 * @param id 发票申请ID
 * @returns
 */
export const doDeleteInvoiceRequest = (id: string) => {
    return del({ url: `addon-invoice/invoice/invoiceRequest/${id}` })
}
