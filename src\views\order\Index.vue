<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-30 17:11:22
-->
<script lang="ts" setup>
import type { CheckboxValueType, TabPaneName } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import AccordionFrom from './components/accordionFrom.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import OrderShipment from './orderShipment/Index.vue'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import useClipboard from 'vue-clipboard3'
import CountDown from '@/views/order/components/count-down/index.vue'
// 测试拆分包裹
import SplitTable from './components/order-split-table/SplitTable'
import SplitTableColumn from './components/order-split-table/split-table-column.vue'
// 发票申请弹窗
import InvoiceDialog from './components/InvoiceDialog.vue'
import { getSplitOrderStatus, queryStatus, TabsName } from '@/composables/useOrderStatus'
import DateUtil from '@/utils/date'
import { doGetOrderList, doGetShopDeliveryConfig, doPostOrderBatchDownloadPrinter, doPutCloseShopOrder } from '@/apis/order'
import { doGetInvoicePreRequest, doGetinvoiceRequestList, doGetBatchInvoicePreRequest } from '@/apis/invoice'
import { useDeliveryOrderList } from '@/store/modules/order'
import { cloneDeep } from 'lodash'
import type { ApiOrder, OrderDataType, OrderListSearchData, ShopOrder, ShopOrderItem, ExtraMap } from './types/order'
import { OrderReceiver, SellTypeEnum } from './types/order'
import useOrderHeader from './hooks/useOrderHeader'
import { calculate, getOrderDeliveryStatus } from './orderDetails/OrderStatusCalculate'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import PlatformComp from './platform.vue'
import { doGetExportData } from '@/apis/exportData'
import { CopyDocument, Download, WarningFilled } from '@element-plus/icons-vue'

/*
 *variable
 */
const $useShopInfoStore = useShopInfoStore()
const { toClipboard } = useClipboard()
const $deliveryOrderList = useDeliveryOrderList()
const $router = useRouter()
const $route = useRoute()
const { divTenThousand } = useConvert()
const routeQuery = $route.query.name
const TabNavArr: Array<any[]> = []
// 第一个Tabs名称
const FirstTabsName = ref('全部订单')
// tab切换部分 当前高亮
const activeTabName = ref(routeQuery ? String(routeQuery) : ' ')
const PageConfig = ref({ size: 10, current: 1, total: 0 })
const isPackUp = ref(false)
// 订单数据
const orderData = reactive<OrderDataType>({
    records: [],
})

let extraMap = ref<ExtraMap>({ AllDeliveryCount: '0', miniDeliveryCount: '0' })
//发货弹窗
const deliverDialog = ref(false)
// 备注弹窗
const noteDialog = ref(false)
// 备注id
const remarkNos = ref<string[]>([])
const currentInitRemark = ref('')
// 当前订单号
const currentNo = ref('')
const currentShopOrderNo = ref('')
const orderListParams = reactive({
    status: routeQuery ? String(routeQuery) : '',
    params: {
        orderNo: $route.query.orderNo || '', // 订单号
        buyerNickname: '', // 买家昵称
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        productName: '', // 商品名称
        receiverName: '', // 收货人姓名
        platform: '', //所属渠道
    },
})
const dateTool = new DateUtil()
const isSelfDelivery = ref(true) // 是否自己发货

// table 数组
const multiSelect = ref<ApiOrder[]>([])
const tableKey = ref(0)

// 发票申请弹窗
const showInvoiceDialog = ref(false)
const invoiceDialogData = ref({
    orderNo: '',
    orderData: {},
    invoiceHeaders: [],
    invoiceTypes: [],
})
provide('reloadParentListFn', () => {
    tableKey.value = Date.now()
    initOrderList()
})
/*
 *lifeCircle
 */
initTabs()
/*
 *function
 */
/**
/**
 * @LastEditors: lexy
 * @description: 循环赋值
 * @returns {*}
 */
function loopAssignment(code: number, data: any) {
    console.log('=== API响应详情 ===')
    console.log('响应码:', code)
    console.log('完整响应数据:', JSON.stringify(data, null, 2))

    if (code !== 200) {
        console.error('API响应失败，code:', code)
        ElMessage.error('订单列表获取失败')
        return
    }

    console.log('=== 数据解析 ===')
    console.log('data.records 类型:', typeof data.records)
    console.log('data.records 是否为数组:', Array.isArray(data.records))
    console.log('data.records 长度:', data.records?.length)
    console.log('data.records 内容:', data.records)

    // 检查数据结构
    if (data.records && data.records.length > 0) {
        console.log('第一条订单数据:', JSON.stringify(data.records[0], null, 2))
    }

    orderData.records = data.records || []
    PageConfig.value.current = data.current
    PageConfig.value.size = data.size
    PageConfig.value.total = data.total
    extraMap.value = data.extraMap

    console.log('=== 赋值后检查 ===')
    console.log('orderData.records 长度:', orderData.records?.length)
    console.log('orderData.records 内容:', orderData.records)
    console.log('PageConfig.value.total:', PageConfig.value.total)

    // 强制触发表格重新渲染
    console.log('强制触发表格重新渲染...')
    tableKey.value = Date.now()

    // 延迟检查数据是否正确渲染
    setTimeout(() => {
        console.log('延迟检查 - orderData.records:', orderData.records)
        console.log('延迟检查 - 表格key:', tableKey.value)

        // 如果数据存在但表格仍显示"暂无数据"，尝试再次强制刷新
        if (orderData.records && orderData.records.length > 0) {
            console.log('数据存在，再次强制刷新表格')
            tableKey.value = Date.now() + 1
        }
    }, 100)

    // 再次延迟检查
    setTimeout(() => {
        console.log('二次延迟检查 - orderData.records长度:', orderData.records?.length)
        if (orderData.records && orderData.records.length > 0) {
            console.log('数据确实存在，但可能表格组件有问题')
            console.log('尝试直接操作DOM检查表格内容')
            const tableElement = document.querySelector('.orderIndex-table')
            console.log('表格DOM元素:', tableElement)
            const emptyElement = document.querySelector('.o_table--empty')
            console.log('空数据提示元素:', emptyElement)
        }
    }, 500)

    return
}

/**
 * @LastEditors: lexy
 * @description: 获取订单列表
 * @returns {*}
 */
async function initOrderList() {
    const params = {
        ...orderListParams.params,
        ...PageConfig.value,
        status: orderListParams.status,
        isMiniAppTip: true,
    }
    console.log('发送订单列表请求 - 参数:', params)
    const { data, code } = await doGetOrderList(params)
    console.log('订单列表API响应:', { data, code })
    loopAssignment(code, data)
}
/**
 * @LastEditors: lexy
 * @description: 初始化tab
 * @returns {*}
 */
function initTabs() {
    for (const key in queryStatus) {
        TabNavArr.push([key, queryStatus[key as keyof typeof queryStatus]])
    }
}

const handleTabChange = async (name: TabPaneName) => {
    orderListParams.status = name as string
    initOrderList()
}
/**
 * @LastEditors: lexy
 * @description: 近一个月/三个月/全部（下拉选择）
 * @param {*} event
 * @returns {*}
 */
const handleDropdownCommand = ($event: string) => {
    activeTabName.value = ' '
    FirstTabsName.value = $event
    if (FirstTabsName.value === '近一个月订单') {
        const startTime = dateTool.getLastMonth(new Date())
        loadHandleTabClick(startTime)
    } else if (FirstTabsName.value === '近三个月订单') {
        const startTime = dateTool.getLastThreeMonth(new Date())
        loadHandleTabClick(startTime)
    } else {
        // 请求全部订单清空时间
        orderListParams.params.startTime = ''
        orderListParams.params.endTime = ''
        initOrderList()
    }
}
/**
 * @LastEditors: lexy
 * @description: Tabs 条件查询
 * @returns {*}
 */
const loadHandleTabClick = async (startTime: string) => {
    const endTime = dateTool.getYMDs(new Date())
    orderListParams.params.startTime = startTime
    orderListParams.params.endTime = endTime
    initOrderList()
}
/**
 * @LastEditors: lexy WAITING_FOR_RECEIVE
 * @description: 批量发货跳转路由 WAITING_FOR_DELIVER
 * @returns {*}
 */
const handleDatchDeliverys = () => {
    if (!multiSelect.value.length) return ElMessage.error('请选择商品')
    if (isSelfDelivery.value === false && $useShopInfoStore.shopInfo.shopType === 'SELF_OWNED') {
        return ElMessage.error({ message: '请在平台端进行发货处理' })
    }
    const payOrder = cloneDeep(multiSelect.value.filter((item) => item.status === 'PAID' && item.shopOrders[0].status === 'OK'))
    let deliverOrder = payOrder
        .filter((item) => {
            let currentShopOrder = item.shopOrders[0].shopOrderItems
            currentShopOrder = currentShopOrder.filter(
                (ite) =>
                    ite.status === 'OK' &&
                    ite.packageStatus === 'WAITING_FOR_DELIVER' &&
                    (!ite.afsStatus || ite.afsStatus === 'NONE' || ite.afsStatus === 'REFUND_REQUEST' || ite.afsStatus === 'RETURN_REFUND_REQUEST'),
            )
            return currentShopOrder.length
        })
        .filter((item) => {
            return !item.extra || item.extra.distributionMode !== 'SHOP_STORE'
        })
    if (!deliverOrder.length) {
        return ElMessage.error('暂无需要发货的商品')
    }
    $deliveryOrderList.SET_ORDER_LIST(deliverOrder)
    $router.push({ name: 'deliveryList' })
}
/**
 * @LastEditors: lexy
 * @description: 获取子组件搜索表单数据
 * @returns {*}
 */
const GetSearchData = async (params: OrderListSearchData) => {
    console.log('搜索参数:', params)
    orderListParams.params = {
        orderNo: params.no,
        buyerNickname: params.buyerNickname,
        productName: params.productName,
        receiverName: params.receiverName,
        startTime: params.startTime,
        endTime: params.endTime,
        platform: params.platform,
    }
    console.log('处理后的搜索参数:', orderListParams.params)

    initOrderList()
}
/**
 * @LastEditors: lexy
 * @description: 商品总价计算
 * @param {*} shopOrderItems
 * @returns {string} TotalPrice
 */
const calculateTotalPrice = (shopOrderItems: ShopOrderItem[] = []) => {
    return shopOrderItems.reduce((pre, item) => {
        return Number(
            divTenThousand(item.dealPrice)
                .mul(item.num)
                .add(pre)
                .add(divTenThousand(item.freightPrice || 0)),
        )
    }, 0)
}
/**
 * @LastEditors: lexy
 * @description: 批量备注
 * @returns {*}
 */
const handleNote = () => {
    if (multiSelect.value.length) {
        remarkNos.value = multiSelect.value.map((item) => item.shopOrders[0].no)
        noteDialog.value = true
        return
    }
    ElMessage.error('请先选择订单')
}

/**
 * @LastEditors: lexy
 * @description: 点击发货
 * @returns {*}
 */
const handleDelivery = (no: string, shopOrderNo: string, row: ApiOrder) => {
    currentNo.value = no
    currentShopOrderNo.value = shopOrderNo
    deliverDialog.value = true
}

/**
 * @LastEditors: lexy
 * @description:默认展示第一个商品的单价
 * @param {*} prc
 * @returns {*}
 */
const unitPrice = computed(() => (shopOrderItems: ShopOrderItem[]) => divTenThousand(shopOrderItems[0].dealPrice))
/**
 * @LastEditors: lexy
 * @description: 商品总数量展示
 * @param {*} prc
 * @returns {*}
 */
const num = computed(() => (shopOrderItems: ShopOrderItem[]) => shopOrderItems.reduce((pre, item) => item.num + pre, 0))

/**
 * 获取收货人信息
 */
const getOrderReceiver = (order: ApiOrder): OrderReceiver => {
    const shopOrderReceiver = order.shopOrders[0].orderReceiver
    return shopOrderReceiver ? shopOrderReceiver : order.orderReceiver
}
const handleOrderCommand = (e, order: ApiOrder) => {
    switch (e) {
        case 'close':
            closeShopOrder(order)
            break

        default:
            break
    }
}
const closeShopOrder = async (order: ApiOrder) => {
    ElMessageBox.confirm('确定取消该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const { code } = await doPutCloseShopOrder(order.no, order.shopOrders[0].no)
        if (code !== 200) return ElMessage.error('订单关闭失败')
        ElMessage.success('订单关闭成功')
        order.shopOrders[0].status = 'SELLER_CLOSED'
    })
}
const handleCommand = (e: 'NOTE') => {
    if (e === 'NOTE') {
        handleNote()
    }
}
const handleRemark = (shopOrder: ShopOrder) => {
    remarkNos.value = [shopOrder.no]
    const remark = initRemark(shopOrder)
    if (remark) {
        currentInitRemark.value = remark
    }
    noteDialog.value = true
}
function initRemark(params: any) {
    if (params?.remark && JSON.parse(params?.remark)?.supplierRemark) {
        return JSON.parse(params?.remark)?.supplierRemark
    }
    return ''
}
const handleRemarkSuccess = () => {
    multiSelect.value = []
    initOrderList()
}
/**
 * @LastEditors: lexy
 * @description: 查看详情
 * @returns {*}
 */
const handleCheckDetails = ({ no: orderNo }: ApiOrder, shopItems: ShopOrderItem[]) => {
    const query = { orderNo }
    const firstItem = shopItems[0]
    shopItems.length === 1 && (query.shopOrderItemsId = firstItem.id)
    const packageId = firstItem.packageId
    packageId && (query.packageId = packageId)
    $router.push({ name: 'detailsIndex', query: query })
}

/**
 * @description: 发票申请
 * @param {ApiOrder} row 订单数据
 * @returns {*}
 */
const handleInvoiceApply = async (row: ApiOrder) => {
    // 检查订单状态是否为已完成
    if (calculate(row).state.status !== '已完成') {
        ElMessage.warning('只有已完成收货并评价的订单才能申请发票')
        return
    }

    // 检查订单号是否存在
    if (!row.no) {
        ElMessage.error('订单号不能为空')
        console.error('订单数据:', row)
        return
    }

    console.log('准备调用发票预检查接口，订单号:', row.no)

    try {
        // 调用发票预检查接口，传递 invoiceOwnerType 参数 (SUPPLIER = 供应商端)
        const { code, data, msg } = await doGetInvoicePreRequest(row.no, 'SUPPLIER')

        if (code !== 200) {
            ElMessage.error(msg || '发票预检查失败')
            return
        }

        // 根据返回结果进行不同处理
        console.log('发票预检查返回数据:', data)
        console.log('invoiceStatus:', data.invoiceStatus)

        switch (data.invoiceStatus) {
            case 'ALLOWED_INVOICING':
                // 可以开票，显示发票申请弹窗
                console.log('显示发票申请弹窗')
                showInvoiceDialog.value = true
                invoiceDialogData.value = {
                    orderNo: row.no,
                    orderData: data,
                    invoiceHeaders: data.invoiceHeaders || [],
                    invoiceTypes: data.invoiceTypes || [],
                }
                break

            case 'SERVER_NOT_SUPPORTED':
                // 不支持开票
                ElMessage.warning('当前服务器不支持开票功能')
                break

            case 'NO_INVOICE_ORDER':
                // 暂无可开票订单，跳转到订单详情发票信息
                $router.push({
                    name: 'detailsIndex',
                    query: {
                        orderNo: row.no,
                        tab: 'invoice', // 用于标识跳转到发票信息tab
                    },
                })
                break

            default:
                console.log('未知的发票状态:', data.invoiceStatus)
                ElMessage.warning('未知的发票状态: ' + (data.invoiceStatus || '无状态'))
                break
        }
    } catch (error) {
        console.error('发票申请预检查失败:', error)
        ElMessage.error('发票申请预检查失败，请稍后重试')
    }
}

/**
 * @description: 发票申请成功回调
 */
const handleInvoiceSuccess = () => {
    showInvoiceDialog.value = false
    ElMessage.success('发票申请提交成功')
    // 可以选择刷新订单列表
    // initOrderList()
}

/**
 * @description: 批量开票
 */
const handleBatchInvoice = async () => {
    // 检查是否选择了订单
    if (!multiSelect.value.length) {
        ElMessage.error('请先选择要开票的订单')
        return
    }

    // 过滤出已完成的订单
    const completedOrders = multiSelect.value.filter((order) => {
        const state = calculate(order).state
        return state.status === '已完成'
    })

    if (completedOrders.length === 0) {
        ElMessage.warning('只有已完成收货并评价的订单才能申请发票')
        return
    }

    if (completedOrders.length !== multiSelect.value.length) {
        ElMessage.warning(`已过滤掉 ${multiSelect.value.length - completedOrders.length} 个不符合开票条件的订单`)
    }

    try {
        // 提取订单号
        const orderNos = completedOrders.map((order) => order.no).join(',')
        console.log('批量开票订单号:', orderNos)

        // 调用批量发票预检查接口
        const { code, data, msg } = await doGetBatchInvoicePreRequest(orderNos, 'SUPPLIER')

        if (code !== 200) {
            ElMessage.error(msg || '批量发票预检查失败')
            return
        }

        console.log('批量发票预检查返回数据:', data)
        console.log('invoiceStatus:', data.invoiceStatus)

        // 根据返回结果进行不同处理
        switch (data.invoiceStatus) {
            case 'ALLOWED_INVOICING':
                // 可以开票，显示发票申请弹窗
                console.log('显示批量发票申请弹窗')
                showInvoiceDialog.value = true
                invoiceDialogData.value = {
                    orderNo: orderNos, // 传递多个订单号
                    orderData: data,
                    invoiceHeaders: data.invoiceHeaders || [],
                    invoiceTypes: data.invoiceTypes || [],
                }
                break

            case 'SERVER_NOT_SUPPORTED':
                // 不支持开票
                ElMessage.warning('当前服务器不支持开票功能')
                break

            case 'NO_INVOICE_ORDER':
                // 暂无可开票订单
                ElMessage.warning('选中的订单中暂无可开票项目')
                break

            default:
                console.log('未知的发票状态:', data.invoiceStatus)
                ElMessage.warning('未知的发票状态: ' + (data.invoiceStatus || '无状态'))
                break
        }
    } catch (error) {
        console.error('批量发票申请预检查异常:', error)
        ElMessage.error('批量发票申请预检查失败，请稍后重试')
    }
}

const handleChangeRow = (val: CheckboxValueType, index: number) => {
    orderData.records[index].checked = val as boolean
    multiSelect.value = orderData.records.filter((ro: any) => ro.checked)
    console.log(multiSelect.value)
}
const copyReceiver = async (receiver: OrderReceiver) => {
    try {
        await toClipboard(`${receiver.name}\n${receiver.mobile}\n${receiver.address}`)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}
const { copyOrderNo, getPaymentType, getOrderAfterSaleStatus, getDeliveryMode } = useOrderHeader()

const initDeliveryConfig = async () => {
    if ($useShopInfoStore.shopInfo.shopType === 'SELF_OWNED') {
        const { data } = await doGetShopDeliveryConfig()
        if (data?.supplierDeliver) {
            isSelfDelivery.value = data?.supplierDeliver === 'OWN'
        }
    }
}
initDeliveryConfig()
// 导出数据
const exportData = async (SearchFromData: any) => {
    if (multiSelect.value.length) {
        let exportShopOrderIdList = multiSelect.value.map((item) => item.shopOrders)
        let exportShopOrderId = exportShopOrderIdList.map((ite) => {
            let ites = ite.map((it: { no: string }) => it.no)
            return ites
        })
        const newExportShopOrderIds: string[] = []
        exportShopOrderId.forEach((ids) => {
            ids.forEach((idData) => newExportShopOrderIds.push(idData))
        })
        const exportShopOrderIds = Array.from(new Set(newExportShopOrderIds))
        const { code, data, msg } = await doGetExportData({ exportShopOrderIds })
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    } else {
        let param = {
            buyerNickname: SearchFromData.buyerNickname,
            startTime: SearchFromData.clinchTime?.[0],
            endTime: SearchFromData.clinchTime?.[1],
            orderNo: SearchFromData.no,
            platformList: SearchFromData.platform,
            receiverName: SearchFromData.receiverName,
            productName: SearchFromData.productName,
            status: activeTabName.value,
            exportShopOrderIds: '',
        }
        param.status = param.status.trim()
        const { code, data, msg } = await doGetExportData(param)
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    }
}
/**
 * 批量下载电子面单
 */
const downloadPrinter = () => {
    doPostOrderBatchDownloadPrinter().then((res) => {
        if (res.code !== 200) return ElMessage.error(res.msg || '下载失败')
        else {
            ElMessage.success('导出成功')
            setTimeout(() => {
                $router.push('/download/center')
            }, 500)
            return
        }
    })
}
</script>

<template>
    <accordion-from :show="false" @search-data="GetSearchData" @search-change="isPackUp = $event" @export-data="exportData" />
    <!-- tab栏切换部分s -->
    <el-tabs v-model="activeTabName" style="margin-top: 15px" @tab-change="handleTabChange">
        <el-tab-pane name=" ">
            <template #label>
                <span>{{ FirstTabsName }}</span>
                <el-icon class="el-icon--right-top">
                    <i-ep-arrow-down />
                </el-icon>
                <el-dropdown placement="bottom-end" trigger="click" @command="handleDropdownCommand">
                    <span class="el-dropdown-link" style="height: 40px" @click.stop="() => {}">
                        <el-icon class="el-icon--right">
                            <i-ep-arrow-down />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item v-for="NameItem in TabsName" :key="NameItem" :command="NameItem">{{ NameItem }}</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </template>
        </el-tab-pane>
        <!-- <el-tab-pane v-for="item in TabNavArr" :key="item[0]" :label="item[1]" :name="item[0]"></el-tab-pane> -->
        <el-tab-pane v-for="item in TabNavArr" :key="item[0]" :name="item[0]">
            <template #label>
                <el-badge v-if="item[0] === 'UN_DELIVERY'" :value="extraMap.AllDeliveryCount" class="item" :max="99">
                    <span>{{ item[1] }}</span>
                </el-badge>
                <span v-else>{{ item[1] }}</span>
            </template>
        </el-tab-pane>
    </el-tabs>
    <!-- tab栏切换部分e -->
    <template v-if="['UN_DELIVERY', ' '].includes(activeTabName)">
        <el-button type="primary" round @click="handleDatchDeliverys"> 批量发货 </el-button>
        <el-button type="primary" round @click="handleNote"> 批量备注 </el-button>
    </template>
    <el-button v-else type="primary" round @click="handleNote">批量备注</el-button>
    <el-button type="success" round @click="handleBatchInvoice">批量开票</el-button>
    <el-dropdown v-if="['UN_RECEIVE', ' '].includes(activeTabName)">
        <span class="el-dropdown-link" style="height: 32px; margin-left: 12px">
            更多操作
            <el-icon class="el-icon--right">
                <i-ep-arrow-down />
            </el-icon>
        </span>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item :icon="Download" @click="downloadPrinter">下载面单</el-dropdown-item>
                <!-- <el-dropdown-item divided>Action 5</el-dropdown-item> -->
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    <div v-if="activeTabName === 'UN_DELIVERY' && +extraMap?.AllDeliveryCount >= 1" class="reminder">
        <el-icon size="16" color="#f72020" style="transform: translateY(3px)"><WarningFilled /></el-icon>
        您共有 <span>{{ extraMap?.AllDeliveryCount || 0 }}</span> 笔待发货的订单，其中小程序端订单有 <span>{{ extraMap?.miniDeliveryCount || 0 }}</span> 笔，超过 48
        小时未发货，将触发支付风险提示，直至暂停小程序交易 ！！！
    </div>
    <split-table :key="tableKey" v-model:checkedItem="multiSelect" class="orderIndex-table" :class="{ packUp: !isPackUp }" style="margin-top: 20px" :data="orderData.records" header-selection>
        <!-- <template #header="{ row }">
            <el-tag v-if="row.type === 'SPIKE'" type="danger" style="margin-right: 5px">秒杀</el-tag>
            <div class="header-table" :class="{ 'is-complete': row.status === 'SYSTEM_CLOSED_' }">
                <div><el-tag v-if="row.isPriority" type="success" plain style="margin-right: 6px">优先发货</el-tag>订单号:{{ row.no }}</div>
                <div>创建时间:{{ row.createTime }}</div>
                               <div v-if="isSplitOrder(row)">订单状态：已拆分</div>
                <div v-if="row.shopOrders[0].packageMap.size > 1">订单状态：已拆分</div>
                <remark-flag :content="initRemark(row.shopOrders[0])" @see-remark="handleRemark(row.shopOrders[0])" />
            </div>
        </template> -->
        <template #header="{ row }">
            <div class="order-header-container">
                <!-- 左侧信息区域 -->
                <div class="order-header-left">
                    <div class="order-basic-info">
                        <platform-comp :platform="row?.platform" />
                        <el-tag v-if="row.type === 'SPIKE'" type="danger" class="spike-tag">秒杀</el-tag>
                        <el-tag v-if="row.isPriority" type="success" plain class="priority-tag">优先发货</el-tag>
                    </div>
                    <div class="order-number-info">
                        <span class="order-label">订单号:</span>
                        <span class="order-number">{{ row.no }}</span>
                        <span class="copy" @click="copyOrderNo(row.no)">
                            <el-icon><CopyDocument /></el-icon>
                        </span>
                    </div>
                    <div class="order-time-info">
                        <span class="time-label">创建时间:</span>
                        <span class="time-value">{{ row.createTime }}</span>
                    </div>
                </div>

                <!-- 右侧状态和金额区域 -->
                <div class="order-header-right">
                    <div class="money-section">
                        <span class="money-label">{{ ['待支付', '已关闭'].includes(calculate(row).state.status) ? '应付款' : '实付款' }}</span>
                        <span class="money-amount">￥{{ calculateTotalPrice(row.shopOrders?.[0].shopOrderItems) }}</span>
                    </div>
                    <div class="status-tags">
                        <div v-if="getPaymentType(row)" class="payment-tag">{{ getPaymentType(row) }}</div>
                        <div v-if="getOrderAfterSaleStatus(row)" class="after-sale-tag">售后中</div>
                        <div v-if="getDeliveryMode(row)" class="distribution-tag">{{ getDeliveryMode(row) }}</div>
                    </div>
                    <div class="remark-section">
                        <remark-flag :content="initRemark(row.shopOrders?.[0])" @see-remark="handleRemark(row.shopOrders?.[0])" />
                    </div>
                </div>
            </div>
        </template>
        <split-table-column prop="name" label="商品" width="350px">
            <template #default="{ shopOrderItems }">
                <!-- 已拆分数据展示 -->
                <div class="orderIndex-table__img-box">
                    <el-image
                        v-for="item in shopOrderItems.slice(0, 2)"
                        :key="item.id"
                        fits="cover"
                        style="width: 63px; height: 63px"
                        shape="square"
                        size="large"
                        :src="item.image"
                        :title="item.productName"
                    />
                    <span class="order-info">
                        <p class="order-info__name">{{ shopOrderItems?.[0]?.productName }}</p>
                        <p class="order-info__spec">{{ shopOrderItems?.[0]?.specs?.join(',') }}</p>
                        <!-- <p class="order-info__selltype">{{ SellTypeEnum[shopOrderItems?.[0]?.sellType] }}</p> -->
                    </span>
                    <div class="orderIndex-table__img-mask">
                        <span>￥{{ unitPrice(shopOrderItems) }}</span>
                        <span style="color: #838383; font-size: 10px">共{{ num(shopOrderItems) }}件</span>
                    </div>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="age" label="客户" width="180px" :is-mixed="true">
            <template #default="{ row }">
                <!-- <div class="avatar_text avatar_text__bottom money_text">
                    <span style="color: #2e99f3; margin-right: 10px">买家昵称 : {{ row.buyerNickname }}</span>
                    <div style="padding: 0 10px 0" class="money_text">
                        (收货人：{{ getOrderReceiver(row).name }},{{ getOrderReceiver(row).mobile }})
                    </div>
                </div> -->
                <div class="customer">
                    <div v-if="!['SHOP_STORE', 'VIRTUAL'].includes(row?.extra?.distributionMode)" class="customer__copy copy" @click="copyReceiver(getOrderReceiver(row))">
                        <el-icon><CopyDocument /></el-icon>
                    </div>
                    <p>{{ getOrderReceiver(row).name }}</p>
                    <p>{{ getOrderReceiver(row).mobile }}</p>
                    <p>{{ getOrderReceiver(row).address }}</p>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="sex" label="状态" :is-mixed="true">
            <template #default="{ row }">
                <!-- <el-row justify="space-between" align="middle">
                    <el-row justify="space-between" align="middle" style="flex-direction: column">
                        <div class="money_text" style="margin-bottom: 5px">{{ getSplitOrderStatus(row, shopOrderItems).desc }}</div>
                        <el-button
                            v-if="
                                activeTabName === 'UN_DELIVERY' &&
                                getSplitOrderStatus(row, shopOrderItems).canDelivery &&
                                (!row.extra || row.extra.distributionMode !== 'SHOP_STORE')
                            "
                            style="width: 68px; height: 36px"
                            type="primary"
                            round
                            @click="handleDelivery(row.no, row.shopOrders[0].no, row)"
                            >发货
                        </el-button>
                    </el-row>
                </el-row> -->
                <div style="text-align: center">
                    <p>{{ calculate(row).state.status }}</p>
                    <count-down v-if="calculate(row).state.status === '待支付'" :create-time="row.createTime" :pay-timeout="row?.timeout?.payTimeout" />
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="deliveryStatus" label="发货状态" width="100">
            <template #default="{ row, shopOrderItems }">
                <div class="delivery">
                    <p>{{ getOrderDeliveryStatus(row, shopOrderItems) }}</p>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="sex" label="操作" width="200" :is-mixed="true">
            <!-- <template #default="{ row, shopOrderItems }">
                <q-dropdown-btn
                    v-if="row.status === 'UNPAID' && row.shopOrders[0].status === 'OK'"
                    title="查看详情"
                    :option="[{ name: 'close', label: '关闭订单' }]"
                    @right-click="(e) => handleOrderCommand(e, row)"
                    @left-click="$router.push({ name: 'detailsIndex', query: { orderNo: row.no } })"
                />
                <el-button
                    v-else
                    type="primary"
                    plain
                    size="large"
                    text
                    bg
                    round
                    style="width: 82px; height: 36px; background: #ecf5fd"
                    class="caozuo_btn"
                    @click="handleCheckDetails(row, shopOrderItems)"
                    >查看详情
                </el-button>
            </template> -->
            <template #default="{ row, shopOrderItems }">
                <div style="display: flex; flex-direction: column; gap: 8px; align-items: center">
                    <el-button
                        v-if="getOrderDeliveryStatus(row, shopOrderItems) && getOrderDeliveryStatus(row, shopOrderItems) !== '已发货' && isSelfDelivery"
                        type="primary"
                        bg
                        size="large"
                        round
                        @click="handleDelivery(row.no, row.shopOrders[0].no, row)"
                        >发货
                    </el-button>
                    <q-dropdown-btn
                        v-if="row.status === 'UNPAID' && row.shopOrders[0].status === 'OK'"
                        title="查看详情"
                        :option="[{ name: 'close', label: '关闭订单' }]"
                        @right-click="(e) => handleOrderCommand(e, row)"
                        @left-click="$router.push({ name: 'detailsIndex', query: { orderNo: row.no } })"
                    />
                    <el-button
                        v-else
                        type="primary"
                        plain
                        size="large"
                        text
                        bg
                        round
                        style="width: 82px; height: 36px; background: #ecf5fd"
                        class="caozuo_btn"
                        @click="handleCheckDetails(row, shopOrderItems)"
                        >查看详情
                    </el-button>
                    <!-- 发票申请按钮 - 只在已完成收货并评价时显示 -->
                    <el-button
                        v-if="calculate(row).state.status === '已完成'"
                        type="success"
                        plain
                        size="large"
                        text
                        bg
                        round
                        style="width: 82px; height: 36px; background: #f0f9ff"
                        class="invoice_btn"
                        @click="handleInvoiceApply(row)"
                        >发票申请
                    </el-button>
                </div>
            </template>
        </split-table-column>
    </split-table>
    <el-row justify="end" align="middle">
        <!-- <el-button type="primary" round @click="handleNote"> 批量备注 </el-button> -->
        <page-manage v-model="PageConfig" :load-init="true" :total="PageConfig.total" @reload="initOrderList" />
    </el-row>
    <!-- 发货的弹窗部分s -->
    <order-shipment v-model:isShow="deliverDialog" :current-no="currentNo" :current-shop-order-no="currentShopOrderNo" />
    <!-- 备注弹窗s -->
    <remark-popup v-model:isShow="noteDialog" v-model:ids="remarkNos" v-model:remark="currentInitRemark" remark-type="GOODS" @success="handleRemarkSuccess" />
    <!-- 备注弹窗e -->

    <!-- 发票申请弹窗 -->
    <InvoiceDialog
        v-model:visible="showInvoiceDialog"
        :order-no="invoiceDialogData.orderNo"
        :order-data="invoiceDialogData.orderData"
        :invoice-headers="invoiceDialogData.invoiceHeaders"
        :invoice-types="invoiceDialogData.invoiceTypes"
        @success="handleInvoiceSuccess"
    />
</template>

<style lang="scss" scoped>
@import '@/assets/css/goods/goods.scss';
// 按钮样式优化
.caozuo_btn {
    background: linear-gradient(135deg, #ecf5fd 0%, #dbeafe 100%) !important;
    border: 1px solid #bfdbfe !important;
    color: #1e40af !important;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);

    &:hover {
        color: #fff !important;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
        border-color: #1d4ed8 !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
}

.invoice_btn {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
    border: 1px solid #bbf7d0 !important;
    color: #166534 !important;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);

    &:hover {
        color: #fff !important;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
        border-color: #16a34a !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
    }
}
@include b(orderIndex-table) {
    overflow-x: auto;
    height: calc(100vh - 520px);
    transition: height 0.5s;
    background: #f5f7fa;
    border-radius: 12px;

    @include e(img-box) {
        width: 330px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }
    @include e(img) {
        flex-shrink: 0;
        border-radius: 8px;
        position: relative;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.05);
        }
    }
    @include e(img-mask) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 13px;
        color: #374151;
        font-weight: 500;
        gap: 4px;
    }
}
@include b(is-complete) {
    background: #eef1f6;
}
// 新的订单头部容器样式
@include b(order-header-container) {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    gap: 24px;
    padding: 4px 0;

    @media (max-width: 1200px) {
        flex-direction: column;
        gap: 16px;
    }
}

@include b(order-header-left) {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .order-basic-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .spike-tag {
            font-size: 11px;
            padding: 2px 6px;
            height: auto;
        }

        .priority-tag {
            font-size: 11px;
            padding: 2px 6px;
            height: auto;
        }
    }

    .order-number-info {
        display: flex;
        align-items: center;
        gap: 6px;

        .order-label {
            font-size: 13px;
            color: #6b7280;
            font-weight: 500;
        }

        .order-number {
            font-size: 14px;
            color: #1f2937;
            font-weight: 600;
            font-family: 'Courier New', monospace;
        }
    }

    .order-time-info {
        display: flex;
        align-items: center;
        gap: 6px;

        .time-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .time-value {
            font-size: 13px;
            color: #374151;
        }
    }
}

@include b(order-header-right) {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    min-width: 200px;

    @media (max-width: 1200px) {
        align-items: flex-start;
        width: 100%;
        min-width: auto;
    }

    .money-section {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #bae6fd;
        display: flex;
        align-items: center;
        gap: 8px;

        .money-label {
            font-size: 12px;
            color: #0369a1;
            font-weight: 500;
        }

        .money-amount {
            font-size: 16px;
            color: #dc2626;
            font-weight: 700;
        }
    }

    .status-tags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        justify-content: flex-end;

        .payment-tag {
            background: #f3f4f6;
            color: #6b7280;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }

        .after-sale-tag {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            border: 1px solid #f59e0b;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
        }

        .distribution-tag {
            background: #ecfdf5;
            color: #059669;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }
    }

    .remark-section {
        margin-top: 4px;
    }
}

// 保留原有的 money_text 样式以防其他地方使用
@include b(money_text) {
    font-size: 13px;
    color: #374151;
    font-weight: 500;
}

@include b(avatar_text_box) {
    @include flex(space-between, flex-start);
}
@include b(send) {
    font-size: 14px;
    color: #333333;
}

@include b(el-icon--right-top) {
    margin-left: 5px;
}
@include b(el-icon--right) {
    padding-top: 15px;
    position: absolute;
    left: -20px;
    opacity: 0;
}
@include b(packUp) {
    height: calc(100vh - 350px);
}
@include b(copy) {
    color: #3b82f6;
    margin-left: 8px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
        background: #dbeafe;
        color: #1d4ed8;
        transform: scale(1.1);
    }
}

@include b(order-info) {
    flex: 1;
    margin: 0 12px;
    word-break: break-all;
    line-height: 1.6;
    overflow: hidden;

    @include e(name) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 600;
        font-size: 14px;
        color: #1f2937;
        margin-bottom: 4px;
    }
    @include e(spec) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-size: 12px;
        color: #6b7280;
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
        margin-bottom: 4px;
    }
    @include e(selltype) {
        margin-top: 8px;
        font-size: 12px;
        color: #059669;
        font-weight: 500;
    }
}
@include b(customer) {
    text-align: left;
    width: 100%;
    line-height: 1.5;
    padding: 14px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    min-width: 160px;

    p {
        margin: 5px 0;
        font-size: 13px;
        color: #374151;
        word-break: break-all;

        &:first-child {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        &:nth-child(2) {
            color: #3b82f6;
            font-weight: 500;
            font-size: 13px;
        }

        &:last-child {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
            margin-top: 6px;
        }
    }

    @include e(copy) {
        text-align: right;
        margin-bottom: 10px;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
            background: #dbeafe;
        }
    }
}
@include b(reminder) {
    min-height: 48px;
    width: 100%;
    line-height: 1.5;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    border-left: 4px solid #ef4444;
    font-size: 14px;
    padding: 12px 16px;
    margin-top: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
        flex-shrink: 0;
    }

    span {
        font-size: 16px;
        color: #dc2626;
        font-weight: 700;
        background: #fee2e2;
        padding: 2px 6px;
        border-radius: 4px;
    }
}
::v-deep .el-badge__content,
::v-deep .is-fixed {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    font-weight: 700;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
    min-width: 18px;
    height: 18px;
    line-height: 14px;
}

// 标签样式优化
::v-deep .el-tag {
    border-radius: 6px;
    font-weight: 500;
    font-size: 12px;
    padding: 4px 8px;
    border: none;

    &.el-tag--success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: #ffffff;
    }

    &.el-tag--danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: #ffffff;
    }

    &.is-plain {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }
}
</style>
<style scoped>
/* 下拉菜单样式优化 */
.example-showcase .el-dropdown-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    color: #374151;
    font-weight: 500;
}

.example-showcase .el-dropdown-link:hover {
    background: #f3f4f6;
    color: #1f2937;
}

/* 按钮组样式优化 */
.el-button.is-round {
    border-radius: 20px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.el-button.is-round.el-button--primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.el-button.is-round.el-button--primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 表格容器整体样式 */
.orderIndex-table .o_table--container {
    margin: 0;
    padding: 0;
    background: transparent;
    box-shadow: none;
}
</style>
