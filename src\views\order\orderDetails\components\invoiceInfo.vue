<template>
    <div class="invoice-info" v-loading="loading">
        <!-- 基本信息卡片 -->
        <div class="info-card">
            <div class="card-header">
                <h3>基本信息</h3>
                <div class="action-buttons">
                    <el-button type="primary" size="small" @click="handleApplyInvoice">申请开票</el-button>
                    <el-button size="small" :loading="downloadLoading" @click="handleDownloadInvoice">下载发票</el-button>
                </div>
            </div>
            <div class="card-content">
                <div class="info-row">
                    <div class="info-item">
                        <span class="label">订单号：</span>
                        <span class="value">{{ orderNo }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">开票金额：</span>
                        <span class="value amount">¥{{ formatMoney(invoiceData.invoiceAmount) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发票详情卡片 -->
        <div class="info-card" v-if="invoiceData.id">
            <div class="card-header">
                <h3>发票详情</h3>
            </div>
            <div class="card-content">
                <div class="detail-grid">
                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">【商家/供应商】</span>
                            <span class="value">{{ invoiceData.shopSupplierName }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">金额：</span>
                            <span class="value amount">¥{{ formatMoney(invoiceData.invoiceAmount) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">抬头类型：</span>
                            <span class="value">{{ getHeaderTypeLabel(invoiceData.invoiceHeaderType) }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">发票类型：</span>
                            <span class="value">{{ getInvoiceTypeLabel(invoiceData.invoiceType) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">税号编码：</span>
                            <span class="value">{{ invoiceData.taxIdentNo }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">发票抬头：</span>
                            <span class="value">{{ invoiceData.header }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">邮箱地址：</span>
                            <span class="value">{{ invoiceData.email }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">开户行：</span>
                            <span class="value">{{ invoiceData.openingBank }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">银行账号：</span>
                            <span class="value">{{ invoiceData.bankAccountNo }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="label">企业电话：</span>
                            <span class="value">{{ invoiceData.enterprisePhone }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">企业地址：</span>
                            <span class="value">{{ invoiceData.enterpriseAddress }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">申请时间：</span>
                            <span class="value">{{ formatDateTime(invoiceData.applicationTime) }}</span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-item full-width">
                            <span class="label">开票状态：</span>
                            <span class="value status" :class="getStatusClass(invoiceData.invoiceStatus)">
                                {{ getStatusLabel(invoiceData.invoiceStatus) }}
                            </span>
                        </div>
                    </div>

                    <div class="detail-row" v-if="invoiceData.billingRemarks">
                        <div class="detail-item full-width">
                            <span class="label">拒绝原因或备注信息：</span>
                            <span class="value">{{ invoiceData.billingRemarks }}</span>
                        </div>
                    </div>
                </div>

                <!-- 底部操作按钮 -->
                <div class="card-footer">
                    <div class="footer-buttons">
                        <el-button size="small" class="cancel-btn" @click="handleCancelApplication">撤销申请</el-button>
                        <el-button size="small" class="goods-btn" @click="handleInvoiceGoods">开票商品</el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !invoiceData.id" class="empty-state">
            <el-empty description="暂无发票申请记录" />
        </div>
    </div>

    <!-- 发票申请弹窗 -->
    <InvoiceDialog
        v-model:visible="showInvoiceDialog"
        :order-no="invoiceDialogData.orderNo"
        :order-data="invoiceDialogData.orderData"
        :invoice-headers="invoiceDialogData.invoiceHeaders"
        :invoice-types="invoiceDialogData.invoiceTypes"
        @success="handleInvoiceSuccess"
    />
</template>

<script setup lang="ts" name="InvoiceInfo">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { doGetinvoiceRequestList, doDownloadInvoiceAttachment, doCancelInvoiceRequest, doGetInvoicePreRequest } from '@/apis/invoice'
import type { ApiOrder } from '@/views/order/types/order'
// 导入发票申请弹窗组件
import InvoiceDialog from '@/views/order/components/InvoiceDialog.vue'

interface Props {
    order?: ApiOrder
}

const props = defineProps<Props>()
const $route = useRoute()

// 响应式数据
const loading = ref(false)
const downloadLoading = ref(false)
const invoiceData = ref<any>({})

// 发票申请弹窗相关
const showInvoiceDialog = ref(false)
const invoiceDialogData = ref({
    orderNo: '',
    orderData: {},
    invoiceHeaders: [],
    invoiceTypes: [],
})

// 获取订单号
const orderNo = computed(() => {
    return props.order?.no || ($route.query.orderNo as string) || ''
})

/**
 * 加载发票申请记录
 */
const loadInvoiceData = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        loading.value = true
        console.log('查询发票申请记录，订单号:', orderNo.value)

        const params = {
            orderNo: orderNo.value,
            applyType: 'OWNER', // 申请类型
            invoiceOwnerType: 'SUPPLIER', // 发票所属方类型（供应商端）
            current: 1,
            size: 1, // 只获取第一条记录
        }

        console.log('发票申请查询参数:', params)
        console.log('接口地址: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest')

        const { code, data, msg } = await doGetinvoiceRequestList(params)

        if (code === 200 && data) {
            console.log('发票申请记录查询成功:', data)
            const records = data.records || data || []
            if (records.length > 0) {
                invoiceData.value = records[0] // 取第一条记录
            } else {
                invoiceData.value = {}
            }
        } else {
            console.error('发票申请记录查询失败:', msg)
            ElMessage.error(msg || '查询发票申请记录失败')
            invoiceData.value = {}
        }
    } catch (error) {
        console.error('查询发票申请记录异常:', error)
        ElMessage.error('查询发票申请记录失败，请稍后重试')
        invoiceData.value = {}
    } finally {
        loading.value = false
    }
}

/**
 * 格式化金额显示
 */
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    return (amount / 100).toFixed(2) // 假设后端返回的是分为单位
}

/**
 * 获取发票类型标签
 */
const getInvoiceTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        VAT_GENERAL: '增值税电子普通发票',
        VAT_SPECIAL: '增值税电子专用发票',
    }
    return labels[type] || type || '增值税电子普通发票'
}

/**
 * 获取抬头类型标签
 */
const getHeaderTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        PERSONAL: '个人',
        ENTERPRISE: '企业',
        COMPANY: '企业',
    }
    return labels[type] || '个人或企业单位'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
        REQUEST_IN_PROCESS: '开票中',
        SUCCESSFULLY_INVOICED: '开票成功',
        FAILED_INVOICE_REQUEST: '开票失败',
        CLIENT_CANCEL_REQUEST: '用户撤销',
    }
    return labels[status] || status || '开票中/开票成功/开票失败'
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status: string) => {
    const classes: Record<string, string> = {
        REQUEST_IN_PROCESS: 'status-processing',
        SUCCESSFULLY_INVOICED: 'status-success',
        FAILED_INVOICE_REQUEST: 'status-failed',
        CLIENT_CANCEL_REQUEST: 'status-cancelled',
    }
    return classes[status] || 'status-processing'
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
    if (!dateTime) return ''
    return new Date(dateTime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    })
}

/**
 * 申请开票
 */
const handleApplyInvoice = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        console.log('发票申请预检查，订单号:', orderNo.value)

        // 调用发票预检查接口
        const { code, data, msg } = await doGetInvoicePreRequest(orderNo.value, 'SUPPLIER')

        if (code !== 200) {
            ElMessage.error(msg || '发票预检查失败')
            return
        }

        console.log('发票预检查返回数据:', data)
        console.log('invoiceStatus:', data.invoiceStatus)

        // 根据返回结果进行不同处理
        switch (data.invoiceStatus) {
            case 'ALLOWED_INVOICING':
                // 可以开票，显示发票申请弹窗
                console.log('显示发票申请弹窗')
                showInvoiceDialog.value = true
                invoiceDialogData.value = {
                    orderNo: orderNo.value,
                    orderData: data,
                    invoiceHeaders: data.invoiceHeaders || [],
                    invoiceTypes: data.invoiceTypes || [],
                }
                break

            case 'SERVER_NOT_SUPPORTED':
                // 不支持开票
                ElMessage.warning('当前服务器不支持开票功能')
                break

            case 'NO_INVOICE_ORDER':
                // 暂无可开票订单
                ElMessage.warning('当前订单暂无可开票项目')
                break

            default:
                console.log('未知的发票状态:', data.invoiceStatus)
                ElMessage.warning('未知的发票状态: ' + (data.invoiceStatus || '无状态'))
                break
        }
    } catch (error) {
        console.error('发票申请预检查异常:', error)
        ElMessage.error('发票申请预检查失败，请稍后重试')
    }
}

/**
 * 发票申请成功回调
 */
const handleInvoiceSuccess = () => {
    // 重新加载发票数据
    loadInvoiceData()
}

/**
 * 下载发票
 */
const handleDownloadInvoice = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        downloadLoading.value = true
        console.log('下载发票，订单号:', orderNo.value)
        console.log('接口地址: https://dev.chongyoulingxi.com/api/addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachmentByShop')

        const response = await doDownloadInvoiceAttachment(orderNo.value)

        // 检查响应是否为blob类型
        if (response.data && response.data instanceof Blob) {
            // 创建下载链接
            const url = window.URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = url

            // 设置默认文件名
            let fileName = `发票_${orderNo.value}.pdf`

            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)

            ElMessage.success('发票下载成功')
        } else {
            ElMessage.error('下载失败，未获取到文件数据')
        }
    } catch (error) {
        console.error('下载发票失败:', error)
        ElMessage.error('下载发票失败，请稍后重试')
    } finally {
        downloadLoading.value = false
    }
}

/**
 * 撤销申请
 */
const handleCancelApplication = async () => {
    if (!invoiceData.value.id) {
        ElMessage.warning('暂无发票信息')
        return
    }

    // 只有开票中的状态才能撤销
    if (invoiceData.value.invoiceStatus !== 'REQUEST_IN_PROCESS') {
        ElMessage.warning('只有开票中的申请才能撤销')
        return
    }

    try {
        // 显示确认对话框
        await ElMessageBox.confirm('确定要撤销此发票申请吗？撤销后将无法恢复。', '撤销确认', {
            confirmButtonText: '确定撤销',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger',
        })

        // 调用撤销申请 API
        console.log('撤销发票申请，ID:', invoiceData.value.id)
        const { code, msg } = await doCancelInvoiceRequest(invoiceData.value.id)

        if (code === 200) {
            ElMessage.success('发票申请已撤销')
            // 重新加载发票数据
            await loadInvoiceData()
        } else {
            console.error('撤销发票申请失败:', msg)
            ElMessage.error(msg || '撤销申请失败，请稍后重试')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('撤销发票申请异常:', error)
            ElMessage.error('撤销申请失败，请稍后重试')
        }
    }
}

/**
 * 开票商品
 */
const handleInvoiceGoods = () => {
    if (!invoiceData.value.id) {
        ElMessage.warning('暂无发票信息')
        return
    }

    // 跳转到开票商品页面或显示商品详情
    ElMessage.info('开票商品功能开发中...')
    // TODO: 实现查看开票商品的逻辑
}

// 组件挂载时加载数据
onMounted(() => {
    loadInvoiceData()
})
</script>

<style scoped lang="scss">
.invoice-info {
    padding: 20px;
    background: #f5f7fa;
    min-height: 500px;

    .info-card {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #eee;

            h3 {
                margin: 0;
                color: #333;
                font-size: 16px;
                font-weight: 600;
            }

            .action-buttons {
                display: flex;
                gap: 8px;
            }
        }

        .card-content {
            padding: 20px;

            .info-row {
                display: flex;
                gap: 40px;

                .info-item {
                    display: flex;
                    align-items: center;

                    .label {
                        color: #666;
                        font-size: 14px;
                        margin-right: 8px;
                    }

                    .value {
                        color: #333;
                        font-size: 14px;

                        &.amount {
                            color: #f56c6c;
                            font-weight: 600;
                        }
                    }
                }
            }

            .detail-grid {
                .detail-row {
                    display: flex;
                    margin-bottom: 16px;
                    gap: 40px;

                    .detail-item {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        &.full-width {
                            flex: 3;
                        }

                        .label {
                            color: #666;
                            font-size: 14px;
                            margin-right: 8px;
                            min-width: 80px;
                        }

                        .value {
                            color: #333;
                            font-size: 14px;

                            &.amount {
                                color: #f56c6c;
                                font-weight: 600;
                            }

                            &.status {
                                &.status-processing {
                                    color: #e6a23c;
                                }

                                &.status-success {
                                    color: #67c23a;
                                }

                                &.status-failed {
                                    color: #f56c6c;
                                }

                                &.status-cancelled {
                                    color: #909399;
                                }
                            }
                        }
                    }
                }
            }
        }

        .card-footer {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
            border-radius: 0 0 8px 8px;

            .footer-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;

                .el-button {
                    min-width: 80px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: 500;
                    padding: 8px 16px;
                    transition: all 0.3s ease;

                    &.cancel-btn {
                        background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
                        border: none;
                        color: white;
                        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);

                        &:hover {
                            background: linear-gradient(135deg, #ff9c99 0%, #ff7875 100%);
                            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
                            transform: translateY(-1px);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }

                    &.goods-btn {
                        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                        border: none;
                        color: white;
                        box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);

                        &:hover {
                            background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
                            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
                            transform: translateY(-1px);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }
                }
            }
        }
    }

    .empty-state {
        background: #fff;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}
</style>
