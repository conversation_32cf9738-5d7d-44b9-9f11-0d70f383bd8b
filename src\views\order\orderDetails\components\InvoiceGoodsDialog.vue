<template>
    <el-dialog v-model="dialogVisible" title="开票商品" width="800px" @close="handleClose">
        <div class="invoice-goods-dialog" v-loading="loading">
            <!-- 商品列表 -->
            <div class="goods-list">
                <div class="goods-header">
                    <div class="header-item">商品图片</div>
                    <div class="header-item">商品名称</div>
                    <div class="header-item">开票金额</div>
                </div>

                <div class="goods-content">
                    <div v-if="goodsList.length === 0 && !loading" class="empty-state">
                        <el-empty description="暂无开票商品信息" />
                    </div>

                    <div v-for="(item, index) in goodsList" :key="index" class="goods-item">
                        <div class="goods-image">
                            <el-image :src="item.image || '/default-goods.png'" :alt="item.productName" fit="cover" class="image" :preview-src-list="item.image ? [item.image] : []">
                                <template #error>
                                    <div class="image-slot">
                                        <el-icon><Picture /></el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </div>

                        <div class="goods-name">
                            <div class="name-text">{{ item.productName || '商品名称' }}</div>
                            <div class="name-desc" v-if="item.productDesc">{{ item.productDesc }}</div>
                        </div>

                        <div class="goods-amount">
                            <span class="amount-text">¥{{ formatMoney(item.amount) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper" v-if="total > 0">
                <el-pagination
                    v-model:current-page="pagination.current"
                    v-model:page-size="pagination.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="InvoiceGoodsDialog">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { doGetInvoiceGoodsInfo } from '@/apis/invoice'

interface Props {
    visible: boolean
    invoiceId: string
}

interface Emits {
    (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const goodsList = ref<any[]>([])
const total = ref(0)
const pagination = ref({
    current: 1,
    size: 10,
})

// 处理弹窗显示状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value),
})

/**
 * 格式化金额显示
 */
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    // 根据实际数据，300000 应该显示为 3000.00，所以是分为单位
    return (amount / 100).toFixed(2)
}

/**
 * 加载开票商品信息
 */
const loadInvoiceGoodsInfo = async () => {
    if (!props.invoiceId) {
        ElMessage.warning('发票ID不能为空')
        return
    }

    try {
        loading.value = true
        console.log('查询开票商品信息，发票ID:', props.invoiceId)
        console.log('分页参数:', pagination.value)

        const params = {
            id: props.invoiceId,
            current: pagination.value.current,
            size: pagination.value.size,
        }

        console.log('开票商品查询参数:', params)
        console.log('接口地址: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest/queryInvoiceGoodsInfo')

        const { code, data, msg } = await doGetInvoiceGoodsInfo(params)

        if (code === 200 && data) {
            console.log('开票商品查询成功:', data)
            goodsList.value = data.records || data.list || []
            total.value = parseInt(data.total) || 0
            console.log('解析后的商品列表:', goodsList.value)
            console.log('总数:', total.value)
        } else {
            console.error('开票商品查询失败:', msg)
            ElMessage.error(msg || '查询开票商品失败')
            goodsList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('查询开票商品异常:', error)
        ElMessage.error('查询开票商品失败，请稍后重试')
        goodsList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
    pagination.value.current = page
    loadInvoiceGoodsInfo()
}

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
    pagination.value.size = size
    pagination.value.current = 1
    loadInvoiceGoodsInfo()
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
    emit('update:visible', false)
    // 重置数据
    goodsList.value = []
    total.value = 0
    pagination.value = {
        current: 1,
        size: 10,
    }
}

// 监听弹窗显示状态
watch(
    () => props.visible,
    (newVal) => {
        if (newVal && props.invoiceId) {
            // 弹窗打开时加载数据
            loadInvoiceGoodsInfo()
        }
    },
)
</script>

<style scoped lang="scss">
.invoice-goods-dialog {
    .goods-list {
        .goods-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f5f7fa;
            border-radius: 6px;
            margin-bottom: 16px;
            font-weight: 600;
            color: #333;

            .header-item {
                &:nth-child(1) {
                    width: 100px;
                    text-align: center;
                }
                &:nth-child(2) {
                    flex: 1;
                    padding-left: 16px;
                }
                &:nth-child(3) {
                    width: 120px;
                    text-align: right;
                    padding-right: 16px;
                }
            }
        }

        .goods-content {
            .empty-state {
                padding: 40px 0;
                text-align: center;
            }

            .goods-item {
                display: flex;
                align-items: center;
                padding: 16px;
                border: 1px solid #ebeef5;
                border-radius: 8px;
                margin-bottom: 12px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #409eff;
                    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
                }

                .goods-image {
                    width: 80px;
                    height: 80px;
                    margin-right: 16px;

                    .image {
                        width: 100%;
                        height: 100%;
                        border-radius: 6px;
                        border: 1px solid #eee;
                    }

                    .image-slot {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 100%;
                        height: 100%;
                        background: #f5f7fa;
                        color: #909399;
                        font-size: 20px;
                        border-radius: 6px;
                    }
                }

                .goods-name {
                    flex: 1;
                    padding-right: 16px;

                    .name-text {
                        font-size: 14px;
                        color: #333;
                        line-height: 1.5;
                        margin-bottom: 4px;
                        word-break: break-all;
                    }

                    .name-desc {
                        font-size: 12px;
                        color: #666;
                        line-height: 1.4;
                    }
                }

                .goods-amount {
                    width: 120px;
                    text-align: right;

                    .amount-text {
                        font-size: 16px;
                        color: #f56c6c;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    .pagination-wrapper {
        margin-top: 20px;
        text-align: center;
    }
}

.dialog-footer {
    text-align: center;
}
</style>
